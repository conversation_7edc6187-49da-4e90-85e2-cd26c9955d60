"""
GPT4mole Embedding Extraction with RDKit Descriptors

This script extracts molecular embeddings from the trained conditional transformer
model using RDKit molecular descriptors for conditioning.

Key features:
- Extracts embeddings at different layers of the transformer
- Supports both conditional and unconditional embedding extraction
- Uses 200+ RDKit descriptors for conditioning
- Batch processing capabilities

Author: Modified for RDKit integration
"""

import torch
import torch.nn.functional as F
import numpy as np
from model import ConditionalTransformerDecoder
from rdkit_descriptors import calculate_molecular_descriptors, get_descriptor_calculator, NUM_FEATURES
from helping_functions import Lang

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Model hyperparameters (must match training script)
output_size = 71                      # Vocabulary size
hidden_size = 512                     # Hidden dimension
condition_vector_size = NUM_FEATURES  # Number of RDKit descriptors
num_heads = 16                        # Number of attention heads
num_layers = 24                       # Number of transformer layers
max_seq_length = 220                  # Maximum sequence length
dropout = 0.1                         # Dropout rate

# Character mappings (same as generation script)
index2char = {
    0: '<PAD>', 1: '<SOS>', 2: '<EOS>', 3: 'C', 4: 'c', 5: 'N', 6: 'n', 7: 'O', 8: 'o',
    9: 'S', 10: 's', 11: 'P', 12: 'p', 13: 'F', 14: 'Cl', 15: 'Br', 16: 'I', 17: '(',
    18: ')', 19: '[', 20: ']', 21: '=', 22: '#', 23: '+', 24: '-', 25: '1', 26: '2',
    27: '3', 28: '4', 29: '5', 30: '6', 31: '7', 32: '8', 33: '9', 34: '0', 35: '@',
    36: 'H', 37: '/', 38: '\\', 39: '.', 40: ':', 41: '%', 42: 'B', 43: 'b', 44: 'Se',
    45: 'se', 46: 'Te', 47: 'te', 48: 'As', 49: 'as', 50: 'Si', 51: 'si', 52: 'Al',
    53: 'al', 54: 'Mg', 55: 'mg', 56: 'Ca', 57: 'ca', 58: 'K', 59: 'k', 60: 'Na',
    61: 'na', 62: 'Li', 63: 'li', 64: 'Zn', 65: 'zn', 66: 'Fe', 67: 'fe', 68: 'Cu',
    69: 'cu', 70: 'Mn'
}

char2index = {char: idx for idx, char in index2char.items()}

# Special tokens
PAD_token = 0
SOS_token = 1
EOS_token = 2

def smiles_to_tensor(smiles):
    """
    Convert SMILES string to tensor.
    
    Args:
        smiles (str): SMILES string
        
    Returns:
        torch.Tensor: Tensor representation of SMILES
    """
    try:
        indices = [SOS_token] + [char2index.get(char, 0) for char in smiles] + [EOS_token]
        return torch.tensor(indices, dtype=torch.long, device=device).unsqueeze(1)
    except Exception as e:
        print(f"Error converting SMILES to tensor: {e}")
        return torch.tensor([SOS_token, EOS_token], dtype=torch.long, device=device).unsqueeze(1)

def extract_embeddings(smiles, model_path='condition_GPT_77M_200C_rdkit.pth', 
                      use_conditions=True, condition_vector=None, layer_idx=-1):
    """
    Extract molecular embeddings from the trained model.

    Args:
        smiles (str): SMILES string
        model_path (str): Path to the trained model
        use_conditions (bool): Whether to use condition vectors
        condition_vector (torch.Tensor, optional): Custom condition vector
        layer_idx (int): Which layer to extract embeddings from (-1 for final layer)

    Returns:
        dict: Dictionary containing different types of embeddings
    """
    # Load the trained model
    model = ConditionalTransformerDecoder(
        output_size, hidden_size, condition_vector_size,
        num_heads, num_layers, max_seq_length, dropout
    ).to(device)

    try:
        model.load_state_dict(torch.load(model_path, map_location=device))
        model.eval()
    except FileNotFoundError:
        print(f"Error: Model file {model_path} not found!")
        return None
    except Exception as e:
        print(f"Error loading model: {e}")
        return None

    # Convert SMILES to tensor
    input_seq = smiles_to_tensor(smiles)
    seq_length = torch.tensor([input_seq.size(0)], device=device)

    with torch.no_grad():
        if use_conditions and condition_vector is not None:
            # Use provided condition vector
            condition_vector = condition_vector.to(device).float().unsqueeze(0)
        elif use_conditions:
            # Calculate condition vector from SMILES
            try:
                descriptors = calculate_molecular_descriptors(smiles)
                condition_vector = torch.tensor(descriptors, dtype=torch.float32, device=device).unsqueeze(0)
            except Exception as e:
                print(f"Error calculating descriptors: {e}")
                # Use fallback values
                descriptor_calc = get_descriptor_calculator()
                fallback_values = descriptor_calc.fallback_values
                condition_vector = torch.tensor(fallback_values, dtype=torch.float32, device=device).unsqueeze(0)
        else:
            # Use zero condition vector (for property prediction tasks)
            condition_vector = torch.zeros(1, condition_vector_size, device=device)

        # Extract embeddings step by step
        embeddings = {}
        
        # 1. Token embeddings
        token_embeddings = model.embedding(input_seq)
        embeddings['token_embeddings'] = token_embeddings.squeeze(1).cpu().numpy()

        # 2. Condition embeddings
        condition_emb = model.condition_embedding_net(condition_vector)
        embeddings['condition_embeddings'] = condition_emb.cpu().numpy()

        # 3. Combined embeddings (token + condition)
        if use_conditions:
            combined_embeddings = token_embeddings + condition_emb.unsqueeze(0)
        else:
            combined_embeddings = token_embeddings
        
        embeddings['combined_embeddings'] = combined_embeddings.squeeze(1).cpu().numpy()

        # 4. Positional embeddings
        pos_embeddings = model.positional_encoding(combined_embeddings)
        embeddings['positional_embeddings'] = pos_embeddings.squeeze(1).cpu().numpy()

        # 5. Layer-wise embeddings
        layer_output = pos_embeddings
        layer_embeddings = []
        
        # Generate causal mask
        tgt_mask = torch.triu(torch.ones(input_seq.size(0), input_seq.size(0), device=device), diagonal=1).bool()
        
        for i, layer in enumerate(model.layers):
            layer_output = layer(layer_output, tgt_mask)
            layer_embeddings.append(layer_output.squeeze(1).cpu().numpy())
            
            # If specific layer requested, break early
            if layer_idx != -1 and i == layer_idx:
                break
        
        embeddings['layer_embeddings'] = layer_embeddings
        
        # 6. Final output embeddings
        if layer_idx == -1:
            final_embeddings = layer_output
        else:
            final_embeddings = torch.tensor(layer_embeddings[layer_idx], device=device).unsqueeze(1)
        
        embeddings['final_embeddings'] = final_embeddings.squeeze(1).cpu().numpy()

        # 7. Pooled embeddings (mean pooling over sequence length)
        pooled_embeddings = torch.mean(final_embeddings, dim=0)
        embeddings['pooled_embeddings'] = pooled_embeddings.cpu().numpy()

        # 8. CLS-like embedding (first token embedding)
        cls_embedding = final_embeddings[0, :]
        embeddings['cls_embedding'] = cls_embedding.cpu().numpy()

    return embeddings

def extract_batch_embeddings(smiles_list, model_path='condition_GPT_77M_200C_rdkit.pth',
                           use_conditions=True, condition_vectors=None, layer_idx=-1):
    """
    Extract embeddings for a batch of SMILES strings.

    Args:
        smiles_list (list): List of SMILES strings
        model_path (str): Path to the trained model
        use_conditions (bool): Whether to use condition vectors
        condition_vectors (list, optional): List of condition vectors
        layer_idx (int): Which layer to extract embeddings from

    Returns:
        list: List of embedding dictionaries
    """
    batch_embeddings = []
    
    for i, smiles in enumerate(smiles_list):
        condition_vector = None
        if condition_vectors is not None and i < len(condition_vectors):
            condition_vector = condition_vectors[i]
        
        embeddings = extract_embeddings(
            smiles, model_path, use_conditions, condition_vector, layer_idx
        )
        
        if embeddings is not None:
            batch_embeddings.append(embeddings)
        
        if (i + 1) % 10 == 0:
            print(f"Processed {i + 1}/{len(smiles_list)} molecules")
    
    return batch_embeddings

def compare_embeddings(smiles1, smiles2, model_path='condition_GPT_77M_200C_rdkit.pth'):
    """
    Compare embeddings between two SMILES strings.

    Args:
        smiles1 (str): First SMILES string
        smiles2 (str): Second SMILES string
        model_path (str): Path to the trained model

    Returns:
        dict: Comparison results including cosine similarities
    """
    emb1 = extract_embeddings(smiles1, model_path)
    emb2 = extract_embeddings(smiles2, model_path)
    
    if emb1 is None or emb2 is None:
        return None
    
    results = {}
    
    # Calculate cosine similarities for different embedding types
    for key in ['pooled_embeddings', 'cls_embedding', 'condition_embeddings']:
        if key in emb1 and key in emb2:
            vec1 = emb1[key].flatten()
            vec2 = emb2[key].flatten()
            
            # Cosine similarity
            cos_sim = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
            results[f'{key}_cosine_similarity'] = cos_sim
            
            # Euclidean distance
            euclidean_dist = np.linalg.norm(vec1 - vec2)
            results[f'{key}_euclidean_distance'] = euclidean_dist
    
    return results

def main():
    """Main function for embedding extraction."""
    print("="*60)
    print("GPT4mole Embedding Extraction with RDKit Descriptors")
    print("="*60)
    
    # Example SMILES strings
    test_smiles = [
        "CCO",  # Ethanol
        "CC(=O)O",  # Acetic acid
        "c1ccccc1",  # Benzene
        "CCN(CC)CC",  # Triethylamine
        "CC(C)(C)O"  # tert-Butanol
    ]
    
    print("Test SMILES:")
    for i, smiles in enumerate(test_smiles, 1):
        print(f"{i}. {smiles}")
    
    # Extract embeddings for test molecules
    print("\nExtracting embeddings...")
    embeddings_list = extract_batch_embeddings(test_smiles)
    
    if embeddings_list:
        print(f"\nSuccessfully extracted embeddings for {len(embeddings_list)} molecules")
        
        # Print embedding shapes
        if embeddings_list[0]:
            emb = embeddings_list[0]
            print("\nEmbedding shapes:")
            for key, value in emb.items():
                if isinstance(value, np.ndarray):
                    print(f"  {key}: {value.shape}")
                elif isinstance(value, list):
                    print(f"  {key}: {len(value)} layers, shape {value[0].shape if value else 'N/A'}")
        
        # Compare first two molecules
        if len(embeddings_list) >= 2:
            print(f"\nComparing embeddings between '{test_smiles[0]}' and '{test_smiles[1]}':")
            comparison = compare_embeddings(test_smiles[0], test_smiles[1])
            if comparison:
                for key, value in comparison.items():
                    print(f"  {key}: {value:.4f}")
    
    print("\n" + "="*60)
    print("Embedding extraction completed!")
    print("="*60)

if __name__ == "__main__":
    main()
