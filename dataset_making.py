"""
GPT4mole: Dataset Classes for Molecular Data Processing

This module contains PyTorch Dataset classes and utilities for handling
molecular SMILES data with conditional information for training.

Author: Wen Xing
License: MIT
"""

from torch.utils.data import Dataset, DataLoader
import numpy as np
import torch
from rdkit_descriptors import calculate_molecular_descriptors, get_descriptor_calculator

# Special tokens for sequence processing
PAD_token = 0  # Padding token for variable-length sequences
SOS_token = 1  # Start-of-sequence token
EOS_token = 2  # End-of-sequence token

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


class SMILESDataset(Dataset):
    """
    PyTorch Dataset for SMILES strings with conditional information.

    This dataset handles molecular SMILES strings paired with condition vectors
    (molecular properties) for training conditional molecular generation models.

    Attributes:
        pairs (list): List of (input_smiles, target_smiles) tuples
        conditions (list): List of condition vectors (molecular properties)
        input_lang (Lang): Language object for input tokenization
        output_lang (Lang): Language object for output tokenization
    """

    def __init__(self, pairs, conditions, input_lang, output_lang):
        """
        Initialize the SMILES dataset.

        Args:
            pairs (list): List of (input_smiles, target_smiles) tuples
            conditions (list): List of condition vectors for each SMILES
            input_lang (Lang): Language object for input tokenization
            output_lang (Lang): Language object for output tokenization
        """
        self.pairs = pairs
        self.conditions = conditions
        self.input_lang = input_lang
        self.output_lang = output_lang

    def __len__(self):
        """Return the number of samples in the dataset."""
        return len(self.pairs)

    def __getitem__(self, idx):
        """
        Get a single sample from the dataset.

        Args:
            idx (int): Index of the sample to retrieve

        Returns:
            tuple: (input_tensor, target_tensor, condition_tensor)
        """
        input_tensor = self.tensorFromSMILES(
            self.input_lang, self.pairs[idx][0])
        target_tensor = self.tensorFromSMILES(
            self.output_lang, self.pairs[idx][1])
        condition_tensor = self.conditions[idx]
        return input_tensor, target_tensor, condition_tensor

    @staticmethod
    def tensorFromSMILES(lang, smiles):
        """
        Convert a SMILES string to a tensor of token indices.

        Args:
            lang (Lang): Language object containing character-to-index mapping
            smiles (str): SMILES string to convert

        Returns:
            torch.Tensor: Tensor of token indices with EOS token appended
        """
        indexes = [lang.char2index[char] for char in smiles]
        indexes.append(EOS_token)  # Append EOS token
        return torch.tensor(indexes, dtype=torch.long)  # Shape: [seq_len]


def collate_fn(batch):
    """
    Custom collate function for batching SMILES data with variable lengths.

    This function handles the batching of variable-length SMILES sequences by:
    1. Adding SOS tokens to input sequences
    2. Padding sequences to the same length within the batch
    3. Converting condition vectors to tensors
    4. All tensors are created on CPU for multiprocessing compatibility

    Args:
        batch (list): List of (input_tensor, target_tensor, condition_tensor) tuples

    Returns:
        tuple: (input_tensors, input_lengths, target_tensors, target_lengths, condition_tensors)
            - input_tensors: Padded input sequences with SOS tokens (CPU)
            - input_lengths: Original sequence lengths before padding (CPU)
            - target_tensors: Padded target sequences (CPU)
            - target_lengths: Original target sequence lengths (CPU)
            - condition_tensors: Batch of condition vectors (CPU)
    """
    input_tensors, target_tensors, condition_tensors = zip(*batch)

    # Calculate lengths before padding
    input_lengths = [len(tensor) for tensor in input_tensors]
    target_lengths = [len(tensor) for tensor in target_tensors]

    # Add SOS_token for input sequences (keep on CPU)
    input_tensors = [torch.cat([torch.tensor([SOS_token], dtype=torch.long),
                                tensor[:-1]], dim=0) for tensor in input_tensors]

    # Pad sequences to the same length within the batch (CPU tensors)
    input_tensors = torch.nn.utils.rnn.pad_sequence(
        input_tensors, padding_value=PAD_token, batch_first=True)
    target_tensors = torch.nn.utils.rnn.pad_sequence(
        target_tensors, padding_value=PAD_token, batch_first=True)

    # Convert lengths and conditions to CPU tensors
    input_lengths = torch.tensor(input_lengths, dtype=torch.long)
    target_lengths = torch.tensor(target_lengths, dtype=torch.long)
    condition_tensors = torch.tensor(
        np.array(condition_tensors), dtype=torch.float)

    return input_tensors, input_lengths, target_tensors, target_lengths, condition_tensors


class SMILESDatasetWithRDKit(Dataset):
    """
    PyTorch Dataset for SMILES strings with on-the-fly RDKit descriptor calculation.

    This dataset calculates molecular descriptors on-the-fly during training,
    eliminating the need to pre-calculate and store large descriptor matrices.
    Includes robust error handling to ensure training stability.

    Attributes:
        smiles_list (list): List of SMILES strings
        input_lang (Lang): Language object for input tokenization
        output_lang (Lang): Language object for output tokenization
        descriptor_calculator: RDKit descriptor calculator instance
        failed_count (int): Counter for failed descriptor calculations
    """

    def __init__(self, smiles_list, input_lang, output_lang, use_cache=False):
        """
        Initialize the SMILES dataset with RDKit descriptor calculation.

        Args:
            smiles_list (list): List of SMILES strings
            input_lang (Lang): Language object for input tokenization
            output_lang (Lang): Language object for output tokenization
            use_cache (bool): Whether to cache calculated descriptors
        """
        self.smiles_list = smiles_list
        self.input_lang = input_lang
        self.output_lang = output_lang
        self.descriptor_calculator = get_descriptor_calculator()
        self.failed_count = 0

        print(
            f"Initialized SMILESDatasetWithRDKit with {len(smiles_list)} molecules")
        print(
            f"Will calculate {self.descriptor_calculator.get_num_features()} RDKit descriptors on-the-fly")

    def __len__(self):
        """Return the number of samples in the dataset."""
        return len(self.smiles_list)

    def __getitem__(self, idx):
        """
        Get a single sample from the dataset with on-the-fly descriptor calculation.

        Args:
            idx (int): Index of the sample to retrieve

        Returns:
            tuple: (input_tensor, target_tensor, condition_tensor)
        """
        smiles = self.smiles_list[idx]

        # Convert SMILES to tensors (input and target are the same for autoregressive training)
        input_tensor = self.tensorFromSMILES(self.input_lang, smiles)
        target_tensor = self.tensorFromSMILES(self.output_lang, smiles)

        # Calculate molecular descriptors on-the-fly
        try:
            condition_vector = calculate_molecular_descriptors(smiles)
            # Convert to tensor
            condition_tensor = torch.tensor(
                condition_vector, dtype=torch.float32)
        except Exception as e:
            # If descriptor calculation fails, use fallback values
            self.failed_count += 1
            if self.failed_count % 1000 == 0:  # Log every 1000 failures
                print(
                    f"Warning: Failed to calculate descriptors for {self.failed_count} molecules so far")

            # Use fallback values
            fallback_values = self.descriptor_calculator.fallback_values
            condition_tensor = torch.tensor(
                fallback_values, dtype=torch.float32)

        return input_tensor, target_tensor, condition_tensor

    @staticmethod
    def tensorFromSMILES(lang, smiles):
        """
        Convert a SMILES string to a tensor of token indices.

        Args:
            lang (Lang): Language object containing character-to-index mapping
            smiles (str): SMILES string to convert

        Returns:
            torch.Tensor: Tensor of token indices with EOS token appended
        """
        indexes = [lang.char2index[char] for char in smiles]
        indexes.append(EOS_token)  # Append EOS token
        return torch.tensor(indexes, dtype=torch.long)  # Shape: [seq_len]

    def get_stats(self):
        """
        Get statistics about descriptor calculation.

        Returns:
            dict: Statistics about the dataset and descriptor calculation
        """
        stats = self.descriptor_calculator.get_cache_stats()
        stats['failed_count'] = self.failed_count
        stats['total_molecules'] = len(self.smiles_list)
        stats['success_rate'] = (
            len(self.smiles_list) - self.failed_count) / len(self.smiles_list)
        return stats
