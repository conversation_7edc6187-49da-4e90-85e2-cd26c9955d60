"""
GPT4mole Training Script with On-the-fly RDKit Descriptor Calculation

This script trains the conditional transformer model using RDKit molecular descriptors
calculated on-the-fly during training, eliminating the need for pre-calculated features.

Key improvements:
- Calculates 200+ RDKit descriptors during training
- Robust error handling for descriptor calculation failures
- Memory efficient (no need to store large descriptor matrices)
- Progress monitoring and statistics reporting

Author: Modified for RDKit integration
"""

import os
import sys
import time
import random
import pandas as pd
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from torch.optim.lr_scheduler import StepLR
from tqdm.auto import tqdm

from model import ConditionalTransformerDecoder, masked_cross_entropy
from dataset_making import SMILESDatasetWithRDKit, collate_fn
from helping_functions import clean_data_smiles_only, prepareDataWithRDKit
from rdkit_descriptors import get_descriptor_calculator, NUM_FEATURES

# Set random seed for reproducibility
random.seed(41)
torch.manual_seed(41)
if torch.cuda.is_available():
    torch.cuda.manual_seed(41)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# Data filtering parameters
MIN_LENGTH = 3    # Minimum SMILES string length
MAX_LENGTH = 200  # Maximum SMILES string length (covers >99% of molecules)

# Training configuration
test = False        # Set to True for testing with smaller dataset
saving_pth = True   # Set to True to save the trained model
num_epochs = 2      # Number of training epochs
batch_size = 40     # Batch size for training
learning_rate = 1e-5  # Learning rate

print("="*60)
print("GPT4mole Training with RDKit Descriptors")
print("="*60)
print(f"Test mode: {test}")
print(f"Epochs: {num_epochs}")
print(f"Batch size: {batch_size}")
print(f"Learning rate: {learning_rate}")
print(f"Min/Max SMILES length: {MIN_LENGTH}/{MAX_LENGTH}")
print(f"Number of RDKit features: {NUM_FEATURES}")
print("="*60)

# Load and prepare data
print('Loading data...')
start_time = time.time()

if test:
    print('Loading test subset...')
    all_df = pd.read_parquet('data/smiles_77M_6_features.parquet')
    all_df = all_df[:100000]  # Use smaller subset for testing
    print(f'Test data selected ({len(all_df)} samples)')
else:
    print('Loading full dataset... (this may take a while)')
    all_df = pd.read_parquet('data/smiles_77M_6_features.parquet')
    print(f'Full dataset loaded ({len(all_df)} samples)')

load_time = time.time() - start_time
print(f'Data loading completed in {load_time:.2f} seconds')

# Clean and prepare data (extract only SMILES strings)
print('Extracting and cleaning SMILES...')
smiles_list = clean_data_smiles_only(all_df)
del all_df  # Free memory

print('Preparing data for training...')
input_lang, output_lang, filtered_smiles = prepareDataWithRDKit(
    "smiles", "smiles", smiles_list, MAX_LENGTH=MAX_LENGTH, MIN_LENGTH=MIN_LENGTH)

print(f"Final dataset size: {len(filtered_smiles)} molecules")
print(f"Input vocabulary size: {input_lang.n_chars}")
print(f"Output vocabulary size: {output_lang.n_chars}")

# Display sample data for verification
sample_smiles = random.choice(filtered_smiles)
print(f"Sample SMILES: {sample_smiles}")

# Test descriptor calculation
print("Testing descriptor calculation...")
descriptor_calc = get_descriptor_calculator()
sample_descriptors = descriptor_calc.calculate_descriptors(sample_smiles)
print(f"Sample descriptors shape: {sample_descriptors.shape}")
print(f"Sample descriptors (first 10): {sample_descriptors[:10]}")

# Model hyperparameters
output_size = output_lang.n_chars     # Vocabulary size from data
hidden_size = 512                     # Hidden dimension
condition_vector_size = NUM_FEATURES  # Number of RDKit descriptors
num_heads = 16                        # Number of attention heads
num_layers = 24                       # Number of transformer layers
max_seq_length = 220                  # Maximum sequence length
dropout = 0.1                         # Dropout rate for regularization

# Ensure minimum vocabulary size for stability
if output_size < 10:
    print(
        f"Warning: Small vocabulary size ({output_size}). Consider using more diverse data.")

print(f"Vocabulary size: {output_size} characters")

print("="*60)
print("Model Configuration:")
print(f"Output size (vocab): {output_size}")
print(f"Hidden size: {hidden_size}")
print(f"Condition vector size: {condition_vector_size}")
print(f"Number of heads: {num_heads}")
print(f"Number of layers: {num_layers}")
print(f"Max sequence length: {max_seq_length}")
print(f"Dropout: {dropout}")
print("="*60)

# Create dataset and data loader
print("Creating dataset...")
dataset = SMILESDatasetWithRDKit(
    filtered_smiles, input_lang, output_lang, use_cache=True)
data_loader = DataLoader(dataset, batch_size=batch_size, num_workers=16,
                         pin_memory=True, persistent_workers=False,
                         shuffle=True, collate_fn=collate_fn)

print(f"Dataset created with {len(dataset)} samples")
print(f"Number of batches: {len(data_loader)}")

# Initialize the conditional transformer model
print("Initializing model...")
decoder_model = ConditionalTransformerDecoder(
    output_size, hidden_size, condition_vector_size, num_heads, num_layers,
    max_seq_length, dropout
)
decoder_model = decoder_model.to(device)


def count_parameters(model):
    """Count the number of trainable parameters in the model."""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


total_params = count_parameters(decoder_model)
print(f"Model initialized with {total_params:,} trainable parameters")

# Initialize optimizer and scheduler
optimizer = optim.Adam(decoder_model.parameters(), lr=learning_rate)
scheduler = StepLR(optimizer, step_size=1, gamma=0.2)
scaler = GradScaler()

print("="*60)
print("Starting Training")
print("="*60)

# Training loop
training_start_time = time.time()
for epoch in range(num_epochs):
    epoch_start_time = time.time()
    total_loss = 0
    decoder_model.train()

    # Progress bar for batches
    data_loader_tqdm = tqdm(enumerate(data_loader),
                            total=len(data_loader),
                            desc=f"Epoch {epoch+1}/{num_epochs}",
                            leave=False)

    for batch_idx, (input_batch, input_lengths, target_batch, target_lengths, condition_batch) in data_loader_tqdm:
        # Move tensors to GPU
        input_batch = input_batch.to(device)
        input_lengths = input_lengths.to(device)
        target_batch = target_batch.to(device)
        target_lengths = target_lengths.to(device)
        condition_batch = condition_batch.to(device)

        # Transpose to (seq_length, batch_size) format expected by model
        input_batch = input_batch.transpose(0, 1)
        target_batch = target_batch.transpose(0, 1)

        optimizer.zero_grad()

        # Forward pass with mixed precision
        with autocast():
            output = decoder_model(input_batch, input_lengths, condition_batch)
            loss = masked_cross_entropy(output, target_batch, target_lengths)

        # Backward pass with gradient scaling
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()

        total_loss += loss.item()

        # Update progress bar
        data_loader_tqdm.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Avg Loss': f'{total_loss/(batch_idx+1):.4f}'
        })

        # Print statistics every 1000 batches
        if (batch_idx + 1) % 20000 == 0:
            avg_loss = total_loss / (batch_idx + 1)
            print(
                f"\nBatch {batch_idx+1}/{len(data_loader)}, Average Loss: {avg_loss:.4f}")

            # Print dataset statistics
            stats = dataset.get_stats()
            print(f"Descriptor calculation stats: {stats}")

            # Clear descriptor cache
            descriptor_calc = get_descriptor_calculator()
            descriptor_calc.cache.clear()
            print("Descriptor cache cleared")

            # Memory cleanup
            import gc
            torch.cuda.empty_cache()
            del input_batch, target_batch, condition_batch, output, loss
            gc.collect()
            print("Cuda cache cleared")

    # End of epoch
    epoch_time = time.time() - epoch_start_time
    avg_epoch_loss = total_loss / len(data_loader)

    print(
        f"\nEpoch {epoch+1}/{num_epochs} completed in {epoch_time:.2f} seconds")
    print(f"Average Loss: {avg_epoch_loss:.4f}")

    # Update learning rate
    scheduler.step()
    current_lr = scheduler.get_last_lr()[0]
    print(f"Learning rate: {current_lr:.6f}")

    # Print final dataset statistics for the epoch
    final_stats = dataset.get_stats()
    print(f"Final epoch stats: {final_stats}")
    print("="*60)

total_training_time = time.time() - training_start_time
print(f"Training completed in {total_training_time:.2f} seconds")

# Save the trained model
if saving_pth:
    model_filename = f'condition_GPT_77M_{NUM_FEATURES}C_rdkit.pth'
    torch.save(decoder_model.state_dict(), model_filename)
    print(f"Model saved as {model_filename}")

    # Save additional information
    info_filename = f'training_info_{NUM_FEATURES}C_rdkit.txt'
    with open(info_filename, 'w') as f:
        f.write(f"Training Information\n")
        f.write(f"==================\n")
        f.write(f"Model: ConditionalTransformerDecoder with RDKit descriptors\n")
        f.write(f"Features: {NUM_FEATURES} RDKit molecular descriptors\n")
        f.write(f"Dataset size: {len(filtered_smiles)} molecules\n")
        f.write(f"Vocabulary size: {output_size}\n")
        f.write(f"Hidden size: {hidden_size}\n")
        f.write(f"Number of layers: {num_layers}\n")
        f.write(f"Number of heads: {num_heads}\n")
        f.write(f"Total parameters: {total_params:,}\n")
        f.write(f"Training epochs: {num_epochs}\n")
        f.write(f"Batch size: {batch_size}\n")
        f.write(f"Learning rate: {learning_rate}\n")
        f.write(f"Final average loss: {avg_epoch_loss:.4f}\n")
        f.write(f"Training time: {total_training_time:.2f} seconds\n")
        f.write(f"Final descriptor stats: {final_stats}\n")

    print(f"Training information saved as {info_filename}")

print("Training script completed successfully!")
