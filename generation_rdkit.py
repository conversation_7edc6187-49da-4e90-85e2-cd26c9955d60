"""
GPT4mole Generation Script with RDKit Descriptors

This script generates molecular SMILES strings using the trained conditional transformer
model with RDKit molecular descriptors. Supports both conditional and unconditional generation.

Key features:
- Uses 200+ RDKit descriptors for conditioning
- Supports target-based generation by providing descriptor values
- Robust error handling for descriptor calculation
- Batch generation capabilities

Author: Modified for RDKit integration
"""

import torch
import torch.nn.functional as F
import numpy as np
import random
from model import ConditionalTransformerDecoder
from rdkit_descriptors import calculate_molecular_descriptors, get_descriptor_calculator, NUM_FEATURES
from helping_functions import Lang

# Set random seed for reproducibility
random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Model hyperparameters (must match training script)
output_size = 71                      # Vocabulary size (update based on your training)
hidden_size = 512                     # Hidden dimension
condition_vector_size = NUM_FEATURES  # Number of RDKit descriptors
num_heads = 16                        # Number of attention heads
num_layers = 24                       # Number of transformer layers
max_seq_length = 220                  # Maximum sequence length
dropout = 0.1                         # Dropout rate

# Character mappings (update these based on your actual vocabulary)
index2char = {
    0: '<PAD>', 1: '<SOS>', 2: '<EOS>', 3: 'C', 4: 'c', 5: 'N', 6: 'n', 7: 'O', 8: 'o',
    9: 'S', 10: 's', 11: 'P', 12: 'p', 13: 'F', 14: 'Cl', 15: 'Br', 16: 'I', 17: '(',
    18: ')', 19: '[', 20: ']', 21: '=', 22: '#', 23: '+', 24: '-', 25: '1', 26: '2',
    27: '3', 28: '4', 29: '5', 30: '6', 31: '7', 32: '8', 33: '9', 34: '0', 35: '@',
    36: 'H', 37: '/', 38: '\\', 39: '.', 40: ':', 41: '%', 42: 'B', 43: 'b', 44: 'Se',
    45: 'se', 46: 'Te', 47: 'te', 48: 'As', 49: 'as', 50: 'Si', 51: 'si', 52: 'Al',
    53: 'al', 54: 'Mg', 55: 'mg', 56: 'Ca', 57: 'ca', 58: 'K', 59: 'k', 60: 'Na',
    61: 'na', 62: 'Li', 63: 'li', 64: 'Zn', 65: 'zn', 66: 'Fe', 67: 'fe', 68: 'Cu',
    69: 'cu', 70: 'Mn'
}

char2index = {char: idx for idx, char in index2char.items()}

# Special tokens
PAD_token = 0
SOS_token = 1
EOS_token = 2

def load_trained_model(model_path='condition_GPT_77M_200C_rdkit.pth'):
    """
    Load the trained conditional transformer model with RDKit descriptors.

    Args:
        model_path (str): Path to the trained model file

    Returns:
        ConditionalTransformerDecoder: Loaded model
    """
    model = ConditionalTransformerDecoder(
        output_size, hidden_size, condition_vector_size,
        num_heads, num_layers, max_seq_length, dropout
    ).to(device)

    try:
        model.load_state_dict(torch.load(model_path, map_location=device))
        model.eval()
        print(f"Model loaded successfully from {model_path}")
        print(f"Model configured for {condition_vector_size} RDKit descriptors")
        return model
    except FileNotFoundError:
        print(f"Error: Model file {model_path} not found!")
        print("Please train the model first using train_rdkit.py")
        return None
    except Exception as e:
        print(f"Error loading model: {e}")
        return None

def generate_molecule(model, condition_vector=None, max_length=200, temperature=1.0, top_k=None):
    """
    Generate a single molecule using the trained model.

    Args:
        model: Trained ConditionalTransformerDecoder model
        condition_vector (torch.Tensor, optional): Condition vector for guided generation
        max_length (int): Maximum length of generated sequence
        temperature (float): Sampling temperature (higher = more random)
        top_k (int, optional): Top-k sampling parameter

    Returns:
        str: Generated SMILES string
    """
    model.eval()
    
    with torch.no_grad():
        # Initialize with SOS token
        generated = [SOS_token]
        input_seq = torch.tensor([[SOS_token]], device=device).transpose(0, 1)
        
        # Use provided condition vector or generate random one
        if condition_vector is None:
            # Generate random condition vector within reasonable ranges
            condition_vector = generate_random_condition_vector()
        
        condition_vector = condition_vector.to(device).float().unsqueeze(0)  # Shape: (1, NUM_FEATURES)
        
        for _ in range(max_length):
            # Forward pass
            seq_lengths = torch.tensor([len(generated)], device=device)
            output = model(input_seq, seq_lengths, condition_vector)
            
            # Get logits for the last token
            logits = output[-1, 0, :]  # Shape: (vocab_size,)
            
            # Apply temperature
            logits = logits / temperature
            
            # Apply top-k filtering if specified
            if top_k is not None:
                top_k_logits, top_k_indices = torch.topk(logits, top_k)
                logits = torch.full_like(logits, float('-inf'))
                logits[top_k_indices] = top_k_logits
            
            # Sample next token
            probs = F.softmax(logits, dim=-1)
            next_token = torch.multinomial(probs, 1).item()
            
            # Stop if EOS token is generated
            if next_token == EOS_token:
                break
                
            generated.append(next_token)
            
            # Update input sequence
            next_token_tensor = torch.tensor([[next_token]], device=device)
            input_seq = torch.cat([input_seq, next_token_tensor], dim=0)
    
    # Convert tokens to SMILES string
    smiles = ''.join([index2char.get(token, '') for token in generated[1:]])  # Skip SOS token
    return smiles

def generate_random_condition_vector():
    """
    Generate a random condition vector with realistic molecular descriptor values.
    
    Returns:
        torch.Tensor: Random condition vector
    """
    descriptor_calc = get_descriptor_calculator()
    # Use fallback values as a base and add some random variation
    base_values = descriptor_calc.fallback_values.copy()
    
    # Add random variation (±20% of base values)
    noise = np.random.normal(0, 0.2, size=base_values.shape)
    random_vector = base_values * (1 + noise)
    
    # Ensure non-negative values for descriptors that should be positive
    random_vector = np.abs(random_vector)
    
    return torch.tensor(random_vector, dtype=torch.float32)

def condition_vector_from_smiles(smiles):
    """
    Calculate condition vector from a reference SMILES string.
    
    Args:
        smiles (str): Reference SMILES string
        
    Returns:
        torch.Tensor: Condition vector calculated from the SMILES
    """
    try:
        descriptors = calculate_molecular_descriptors(smiles)
        return torch.tensor(descriptors, dtype=torch.float32)
    except Exception as e:
        print(f"Error calculating descriptors for {smiles}: {e}")
        return generate_random_condition_vector()

def generate_molecules_batch(model, num_molecules=10, condition_vectors=None, 
                           max_length=200, temperature=1.0, top_k=None):
    """
    Generate multiple molecules in batch.

    Args:
        model: Trained model
        num_molecules (int): Number of molecules to generate
        condition_vectors (list, optional): List of condition vectors
        max_length (int): Maximum length of generated sequences
        temperature (float): Sampling temperature
        top_k (int, optional): Top-k sampling parameter

    Returns:
        list: List of generated SMILES strings
    """
    molecules = []
    
    for i in range(num_molecules):
        if condition_vectors is not None and i < len(condition_vectors):
            condition_vector = condition_vectors[i]
        else:
            condition_vector = None
            
        smiles = generate_molecule(model, condition_vector, max_length, temperature, top_k)
        molecules.append(smiles)
        
        if (i + 1) % 10 == 0:
            print(f"Generated {i + 1}/{num_molecules} molecules")
    
    return molecules

def main():
    """Main function for molecule generation."""
    print("="*60)
    print("GPT4mole Molecule Generation with RDKit Descriptors")
    print("="*60)
    
    # Load the trained model
    model = load_trained_model()
    if model is None:
        return
    
    print(f"Using device: {device}")
    print(f"Model vocabulary size: {output_size}")
    print(f"Number of RDKit descriptors: {condition_vector_size}")
    
    # Example 1: Generate molecules with random conditions
    print("\n" + "="*40)
    print("Generating molecules with random conditions:")
    print("="*40)
    
    random_molecules = generate_molecules_batch(
        model, num_molecules=5, temperature=0.8, top_k=50
    )
    
    for i, smiles in enumerate(random_molecules, 1):
        print(f"{i}. {smiles}")
    
    # Example 2: Generate molecules based on a reference SMILES
    print("\n" + "="*40)
    print("Generating molecules based on reference SMILES:")
    print("="*40)
    
    reference_smiles = "CCO"  # Ethanol as reference
    print(f"Reference SMILES: {reference_smiles}")
    
    reference_condition = condition_vector_from_smiles(reference_smiles)
    
    similar_molecules = generate_molecules_batch(
        model, num_molecules=3, 
        condition_vectors=[reference_condition] * 3,
        temperature=0.7, top_k=30
    )
    
    for i, smiles in enumerate(similar_molecules, 1):
        print(f"{i}. {smiles}")
    
    print("\n" + "="*60)
    print("Generation completed!")
    print("="*60)

if __name__ == "__main__":
    main()
