"""
Comprehensive test script for GPT4mole with RDKit descriptors.

This script validates that all components are working correctly before training:
- RDKit descriptor calculation
- Dataset creation and loading
- Model initialization
- Data pipeline integrity
- Memory usage estimation

Run this script before starting training to catch any issues early.
"""

import sys
import time
import traceback
import pandas as pd
import torch
import numpy as np
from torch.utils.data import DataLoader

# Import our modules
try:
    from rdkit_descriptors import (
        calculate_molecular_descriptors, 
        get_descriptor_calculator, 
        NUM_FEATURES,
        batch_calculate_descriptors
    )
    from dataset_making import SMILESDatasetWithRDKit, collate_fn
    from helping_functions import clean_data_smiles_only, prepareDataWithRDKit
    from model import ConditionalTransformerDecoder
    print("✓ All imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

def test_rdkit_installation():
    """Test RDKit installation and basic functionality."""
    print("\n" + "="*50)
    print("Testing RDKit Installation")
    print("="*50)
    
    try:
        from rdkit import Chem
        from rdkit.Chem import Descriptors
        print("✓ RDKit imported successfully")
        
        # Test basic molecule creation
        mol = Chem.MolFromSmiles("CCO")
        if mol is not None:
            print("✓ Basic molecule creation works")
        else:
            print("✗ Failed to create molecule from SMILES")
            return False
            
        # Test descriptor calculation
        mw = Descriptors.MolWt(mol)
        print(f"✓ Basic descriptor calculation works (MW: {mw:.2f})")
        
        return True
    except Exception as e:
        print(f"✗ RDKit test failed: {e}")
        return False

def test_descriptor_calculation():
    """Test our custom descriptor calculation module."""
    print("\n" + "="*50)
    print("Testing Descriptor Calculation")
    print("="*50)
    
    try:
        # Test basic calculation
        test_smiles = ["CCO", "CC(=O)O", "c1ccccc1"]
        
        for smiles in test_smiles:
            descriptors = calculate_molecular_descriptors(smiles)
            print(f"✓ {smiles}: {descriptors.shape} descriptors calculated")
            
            # Check for NaN/Inf values
            if np.any(np.isnan(descriptors)) or np.any(np.isinf(descriptors)):
                print(f"✗ {smiles}: Contains NaN or Inf values")
                return False
        
        # Test invalid SMILES handling
        invalid_descriptors = calculate_molecular_descriptors("INVALID_SMILES")
        print(f"✓ Invalid SMILES handled: {invalid_descriptors.shape}")
        
        # Test batch calculation
        batch_descriptors = batch_calculate_descriptors(test_smiles)
        print(f"✓ Batch calculation: {batch_descriptors.shape}")
        
        # Test caching
        calc = get_descriptor_calculator()
        stats = calc.get_cache_stats()
        print(f"✓ Caching system: {stats}")
        
        print(f"✓ Total features: {NUM_FEATURES}")
        
        return True
    except Exception as e:
        print(f"✗ Descriptor calculation test failed: {e}")
        traceback.print_exc()
        return False

def test_data_loading():
    """Test data loading and preparation."""
    print("\n" + "="*50)
    print("Testing Data Loading")
    print("="*50)
    
    try:
        # Load small subset of data
        print("Loading data subset...")
        df = pd.read_parquet('data/smiles_77M_6_features.parquet')
        print(f"✓ Data loaded: {df.shape}")
        
        # Test with small subset
        test_df = df.head(1000)
        smiles_list = clean_data_smiles_only(test_df)
        print(f"✓ SMILES extracted: {len(smiles_list)} molecules")
        
        # Test data preparation
        input_lang, output_lang, filtered_smiles = prepareDataWithRDKit(
            "smiles", "smiles", smiles_list, MAX_LENGTH=200, MIN_LENGTH=3
        )
        print(f"✓ Data prepared: {len(filtered_smiles)} filtered SMILES")
        print(f"✓ Vocabulary sizes: input={input_lang.n_chars}, output={output_lang.n_chars}")
        
        return True, (input_lang, output_lang, filtered_smiles)
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        traceback.print_exc()
        return False, None

def test_dataset_creation(data_tuple):
    """Test dataset creation and data loading."""
    print("\n" + "="*50)
    print("Testing Dataset Creation")
    print("="*50)
    
    if data_tuple is None:
        print("✗ Skipping dataset test (no data)")
        return False, None
    
    try:
        input_lang, output_lang, filtered_smiles = data_tuple
        
        # Create dataset
        dataset = SMILESDatasetWithRDKit(
            filtered_smiles[:100],  # Use only 100 molecules for testing
            input_lang, 
            output_lang, 
            use_cache=True
        )
        print(f"✓ Dataset created: {len(dataset)} samples")
        
        # Test single sample
        sample = dataset[0]
        input_tensor, target_tensor, condition_tensor = sample
        print(f"✓ Sample shapes: input={input_tensor.shape}, target={target_tensor.shape}, condition={condition_tensor.shape}")
        
        # Test data loader
        data_loader = DataLoader(dataset, batch_size=4, shuffle=True, collate_fn=collate_fn)
        print(f"✓ DataLoader created: {len(data_loader)} batches")
        
        # Test one batch
        batch = next(iter(data_loader))
        input_batch, input_lengths, target_batch, target_lengths, condition_batch = batch
        print(f"✓ Batch shapes:")
        print(f"  Input: {input_batch.shape}")
        print(f"  Target: {target_batch.shape}")
        print(f"  Conditions: {condition_batch.shape}")
        print(f"  Lengths: {input_lengths.shape}, {target_lengths.shape}")
        
        # Get dataset stats
        stats = dataset.get_stats()
        print(f"✓ Dataset stats: {stats}")
        
        return True, (dataset, data_loader)
    except Exception as e:
        print(f"✗ Dataset creation test failed: {e}")
        traceback.print_exc()
        return False, None

def test_model_initialization():
    """Test model initialization and forward pass."""
    print("\n" + "="*50)
    print("Testing Model Initialization")
    print("="*50)
    
    try:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"✓ Using device: {device}")
        
        # Model parameters
        output_size = 71
        hidden_size = 512
        condition_vector_size = NUM_FEATURES
        num_heads = 16
        num_layers = 24
        max_seq_length = 220
        dropout = 0.1
        
        # Initialize model
        model = ConditionalTransformerDecoder(
            output_size, hidden_size, condition_vector_size,
            num_heads, num_layers, max_seq_length, dropout
        ).to(device)
        
        # Count parameters
        total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"✓ Model initialized: {total_params:,} parameters")
        
        # Test forward pass
        batch_size = 4
        seq_length = 20
        
        input_seq = torch.randint(0, output_size, (seq_length, batch_size), device=device)
        seq_lengths = torch.randint(10, seq_length, (batch_size,), device=device)
        condition_vector = torch.randn(batch_size, condition_vector_size, device=device)
        
        with torch.no_grad():
            output = model(input_seq, seq_lengths, condition_vector)
            print(f"✓ Forward pass successful: {output.shape}")
        
        return True, model
    except Exception as e:
        print(f"✗ Model initialization test failed: {e}")
        traceback.print_exc()
        return False, None

def test_training_step(model, dataset_tuple):
    """Test a single training step."""
    print("\n" + "="*50)
    print("Testing Training Step")
    print("="*50)
    
    if model is None or dataset_tuple is None:
        print("✗ Skipping training test (no model or dataset)")
        return False
    
    try:
        from model import masked_cross_entropy
        import torch.optim as optim
        from torch.cuda.amp import GradScaler, autocast
        
        dataset, data_loader = dataset_tuple
        device = next(model.parameters()).device
        
        # Setup optimizer
        optimizer = optim.Adam(model.parameters(), lr=1e-4)
        scaler = GradScaler()
        
        # Get one batch
        batch = next(iter(data_loader))
        input_batch, input_lengths, target_batch, target_lengths, condition_batch = batch
        
        # Transpose to expected format
        input_batch = input_batch.transpose(0, 1)
        target_batch = target_batch.transpose(0, 1)
        
        print(f"✓ Batch prepared: input={input_batch.shape}, target={target_batch.shape}")
        
        # Training step
        model.train()
        optimizer.zero_grad()
        
        with autocast():
            output = model(input_batch, input_lengths, condition_batch)
            loss = masked_cross_entropy(output, target_batch, target_lengths)
        
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
        
        print(f"✓ Training step successful: loss={loss.item():.4f}")
        
        return True
    except Exception as e:
        print(f"✗ Training step test failed: {e}")
        traceback.print_exc()
        return False

def estimate_memory_usage():
    """Estimate memory usage for training."""
    print("\n" + "="*50)
    print("Memory Usage Estimation")
    print("="*50)
    
    try:
        # Model parameters
        total_params = 71 * 512 + 512 * NUM_FEATURES + 512 * 512 * 2  # Rough estimate
        model_memory_mb = total_params * 4 / (1024 * 1024)  # 4 bytes per float32
        
        # Batch memory (rough estimate)
        batch_size = 40
        seq_length = 200
        batch_memory_mb = batch_size * seq_length * 512 * 4 / (1024 * 1024)
        
        print(f"✓ Estimated model memory: {model_memory_mb:.1f} MB")
        print(f"✓ Estimated batch memory: {batch_memory_mb:.1f} MB")
        print(f"✓ Total estimated memory: {model_memory_mb + batch_memory_mb:.1f} MB")
        
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            print(f"✓ Available GPU memory: {gpu_memory:.1f} GB")
            
            if (model_memory_mb + batch_memory_mb) / 1024 < gpu_memory * 0.8:
                print("✓ Memory usage looks reasonable")
            else:
                print("⚠ Warning: High memory usage expected")
        
        return True
    except Exception as e:
        print(f"✗ Memory estimation failed: {e}")
        return False

def main():
    """Run all tests."""
    print("GPT4mole RDKit Setup Validation")
    print("="*60)
    
    tests_passed = 0
    total_tests = 6
    
    # Test 1: RDKit installation
    if test_rdkit_installation():
        tests_passed += 1
    
    # Test 2: Descriptor calculation
    if test_descriptor_calculation():
        tests_passed += 1
    
    # Test 3: Data loading
    data_success, data_tuple = test_data_loading()
    if data_success:
        tests_passed += 1
    
    # Test 4: Dataset creation
    dataset_success, dataset_tuple = test_dataset_creation(data_tuple)
    if dataset_success:
        tests_passed += 1
    
    # Test 5: Model initialization
    model_success, model = test_model_initialization()
    if model_success:
        tests_passed += 1
    
    # Test 6: Training step
    if test_training_step(model, dataset_tuple):
        tests_passed += 1
    
    # Bonus: Memory estimation
    estimate_memory_usage()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! You're ready to start training.")
        print("\nTo start training:")
        print("  python train_rdkit.py")
    else:
        print("❌ Some tests failed. Please fix the issues before training.")
        print("\nCommon solutions:")
        print("  - Install RDKit: conda install -c conda-forge rdkit")
        print("  - Check data file exists: data/smiles_77M_6_features.parquet")
        print("  - Ensure sufficient GPU memory")
    
    print("="*60)

if __name__ == "__main__":
    main()
