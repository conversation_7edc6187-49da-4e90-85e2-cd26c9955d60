# GPT4mole with RDKit Descriptors

This is an enhanced version of GPT4mole that calculates 209 RDKit molecular descriptors on-the-fly during training, replacing the original 6 pre-calculated features. This approach provides much richer molecular representation while being memory efficient.

## 🚀 Key Features

- **On-the-fly descriptor calculation**: Calculates 209 RDKit descriptors during training
- **Memory efficient**: No need to store large descriptor matrices
- **Robust error handling**: Graceful handling of invalid SMILES with fallback values
- **Caching system**: Speeds up training by caching calculated descriptors
- **Progress monitoring**: Real-time statistics on descriptor calculation success rates

## 📊 RDKit Descriptors (209 features)

The model now uses 209 molecular descriptors including:
- **Electronic properties**: EState indices, partial charges, QED
- **Molecular properties**: Molecular weight, heavy atom count, exact mass
- **Topological descriptors**: Chi indices, Kappa indices, Balaban J
- **Surface area descriptors**: TPSA, LabuteASA, VSA descriptors
- **Pharmacophore features**: Lipinski descriptors, functional group counts
- **3D descriptors**: BCUT descriptors, molecular refractivity

## 🛠️ Installation

### Prerequisites
```bash
# Install RDKit (conda recommended)
conda install -c conda-forge rdkit

# Or using pip
pip install rdkit

# Install other dependencies
pip install torch torchvision torchaudio
pip install pandas numpy tqdm
```

### Quick Setup
```bash
git clone <your-repo>
cd GPT4mole_rdkit_cond
pip install -r requirements.txt
```

## 📁 File Structure

```
GPT4mole_rdkit_cond/
├── rdkit_descriptors.py        # RDKit descriptor calculation module
├── train_rdkit.py              # Training script with RDKit descriptors
├── generation_rdkit.py         # Generation script with RDKit descriptors
├── embedding_rdkit.py          # Embedding extraction with RDKit descriptors
├── dataset_making.py           # Updated dataset classes
├── helping_functions.py        # Updated helper functions
├── model.py                    # Transformer model (unchanged)
├── test_rdkit_setup.py         # Test script to validate setup
├── data/                       # Data directory
│   └── smiles_77M_6_features.parquet
└── README_RDKit.md            # This file
```

## 🔧 Usage

### 1. Test Your Setup
```bash
python test_rdkit_setup.py
```

### 2. Training with RDKit Descriptors
```bash
# Test mode (smaller dataset)
python train_rdkit.py  # Set test=True in the script

# Full training
python train_rdkit.py  # Set test=False in the script
```

### 3. Generate Molecules
```bash
python generation_rdkit.py
```

### 4. Extract Embeddings
```bash
python embedding_rdkit.py
```

## ⚙️ Configuration

### Training Parameters (train_rdkit.py)
```python
# Data filtering
MIN_LENGTH = 3          # Minimum SMILES length
MAX_LENGTH = 200        # Maximum SMILES length

# Training configuration
test = False            # Set True for testing with smaller dataset
num_epochs = 2          # Number of training epochs
batch_size = 40         # Batch size
learning_rate = 1e-4    # Learning rate

# Model architecture
hidden_size = 512       # Hidden dimension
num_heads = 16          # Number of attention heads
num_layers = 24         # Number of transformer layers
```

### RDKit Descriptor Configuration
The descriptor calculator automatically handles:
- **Error recovery**: Invalid SMILES get fallback values
- **Caching**: Calculated descriptors are cached for efficiency
- **Normalization**: Extreme values are clipped to prevent training instability

## 📈 Performance Monitoring

The training script provides real-time monitoring of:
- **Descriptor calculation success rate**
- **Cache hit rate**
- **Failed SMILES count**
- **Training loss progression**

Example output:
```
Descriptor calculation stats: {
    'cache_size': 50000,
    'failed_smiles_count': 123,
    'success_rate': 0.9975,
    'cache_enabled': True
}
```

## 🧪 Testing and Validation

### Test RDKit Setup
```python
from rdkit_descriptors import calculate_molecular_descriptors

# Test with simple molecules
smiles = "CCO"  # Ethanol
descriptors = calculate_molecular_descriptors(smiles)
print(f"Calculated {len(descriptors)} descriptors")
```

### Validate Training Data
```python
from dataset_making import SMILESDatasetWithRDKit
from helping_functions import clean_data_smiles_only, prepareDataWithRDKit

# Load and test data preparation
import pandas as pd
df = pd.read_parquet('data/smiles_77M_6_features.parquet')
smiles_list = clean_data_smiles_only(df[:1000])  # Test with 1000 molecules

# Test dataset creation
input_lang, output_lang, filtered_smiles = prepareDataWithRDKit(
    "smiles", "smiles", smiles_list, MAX_LENGTH=200, MIN_LENGTH=3
)

dataset = SMILESDatasetWithRDKit(filtered_smiles, input_lang, output_lang)
print(f"Dataset created with {len(dataset)} molecules")
```

## 🚨 Error Handling

The system includes robust error handling for:

1. **Invalid SMILES**: Automatically uses fallback descriptor values
2. **RDKit calculation failures**: Graceful degradation with reasonable defaults
3. **Memory issues**: Efficient caching and batch processing
4. **Training interruptions**: Model checkpointing and resume capability

## 🔍 Troubleshooting

### Common Issues

1. **RDKit not installed**
   ```bash
   conda install -c conda-forge rdkit
   ```

2. **CUDA out of memory**
   - Reduce batch_size in train_rdkit.py
   - Use gradient accumulation

3. **Slow descriptor calculation**
   - Ensure caching is enabled
   - Consider pre-calculating for very large datasets

4. **High failure rate**
   - Check SMILES data quality
   - Verify RDKit installation

### Performance Tips

1. **Enable caching**: Set `use_cache=True` in dataset creation
2. **Batch processing**: Use appropriate batch sizes for your GPU
3. **Mixed precision**: Training script uses automatic mixed precision
4. **Data filtering**: Remove very long/short SMILES to improve efficiency

## 📊 Expected Results

With 209 RDKit descriptors, you should expect:
- **Richer molecular representation**: Better capture of molecular properties
- **Improved generation quality**: More chemically meaningful molecules
- **Better property control**: More precise conditioning on molecular properties
- **Longer training time**: ~20-30% increase due to descriptor calculation

## 🤝 Contributing

When contributing:
1. Test with `test_rdkit_setup.py`
2. Ensure backward compatibility
3. Add appropriate error handling
4. Update documentation

## 📄 License

Same as original GPT4mole project.

## 🙏 Acknowledgments

- Original GPT4mole authors
- RDKit development team
- PyTorch community
