"""
RDKit molecular descriptor calculation module for on-the-fly feature computation.

This module provides robust calculation of molecular descriptors using RDKit,
with comprehensive error handling to ensure training stability.
"""

import numpy as np
from rdkit import Chem
from rdkit.Chem import Descriptors, rdMolDescriptors, QED
import logging
from typing import Optional, List, Dict, Any
import warnings

# Suppress RDKit warnings to avoid cluttering training logs
warnings.filterwarnings('ignore')
logging.getLogger('rdkit').setLevel(logging.ERROR)

# Complete list of molecular features to calculate
MOLECULE_FEATURES = [
    'MaxAbsEStateIndex', 'MaxEStateIndex', 'MinAbsEStateIndex', 'MinEStateIndex',
    'qed', 'MolWt', 'HeavyAtomMolWt', 'ExactMolWt', 'NumValenceElectrons',
    'NumRadicalElectrons', 'MaxPartialCharge', 'MinPartialCharge',
    'MaxAbsPartialCharge', 'MinAbsPartialCharge', 'FpDensityMorgan1',
    'FpDensityMorgan2', 'FpDensityMorgan3', 'BCUT2D_MWHI', 'BCUT2D_MWLOW',
    'BCUT2D_CHGHI', 'BCUT2D_CHGLO', 'BCUT2D_LOGPHI', 'BCUT2D_LOGPLOW',
    'BCUT2D_MRHI', 'BCUT2D_MRLOW', 'AvgIpc', 'BalabanJ', 'BertzCT',
    'Chi0', 'Chi0n', 'Chi0v', 'Chi1', 'Chi1n', 'Chi1v', 'Chi2n', 'Chi2v',
    'Chi3n', 'Chi3v', 'Chi4n', 'Chi4v', 'HallKierAlpha', 'Ipc', 'Kappa1',
    'Kappa2', 'Kappa3', 'LabuteASA', 'PEOE_VSA1', 'PEOE_VSA10', 'PEOE_VSA11',
    'PEOE_VSA12', 'PEOE_VSA13', 'PEOE_VSA14', 'PEOE_VSA2', 'PEOE_VSA3',
    'PEOE_VSA4', 'PEOE_VSA5', 'PEOE_VSA6', 'PEOE_VSA7', 'PEOE_VSA8',
    'PEOE_VSA9', 'SMR_VSA1', 'SMR_VSA10', 'SMR_VSA2', 'SMR_VSA3', 'SMR_VSA4',
    'SMR_VSA5', 'SMR_VSA6', 'SMR_VSA7', 'SMR_VSA8', 'SMR_VSA9', 'SlogP_VSA1',
    'SlogP_VSA10', 'SlogP_VSA11', 'SlogP_VSA12', 'SlogP_VSA2', 'SlogP_VSA3',
    'SlogP_VSA4', 'SlogP_VSA5', 'SlogP_VSA6', 'SlogP_VSA7', 'SlogP_VSA8',
    'SlogP_VSA9', 'TPSA', 'EState_VSA1', 'EState_VSA10', 'EState_VSA11',
    'EState_VSA2', 'EState_VSA3', 'EState_VSA4', 'EState_VSA5', 'EState_VSA6',
    'EState_VSA7', 'EState_VSA8', 'EState_VSA9', 'VSA_EState1', 'VSA_EState10',
    'VSA_EState2', 'VSA_EState3', 'VSA_EState4', 'VSA_EState5', 'VSA_EState6',
    'VSA_EState7', 'VSA_EState8', 'VSA_EState9', 'FractionCSP3', 'HeavyAtomCount',
    'NHOHCount', 'NOCount', 'NumAliphaticCarbocycles', 'NumAliphaticHeterocycles',
    'NumAliphaticRings', 'NumAromaticCarbocycles', 'NumAromaticHeterocycles',
    'NumAromaticRings', 'NumHAcceptors', 'NumHDonors', 'NumHeteroatoms',
    'NumRotatableBonds', 'NumSaturatedCarbocycles', 'NumSaturatedHeterocycles',
    'NumSaturatedRings', 'RingCount', 'MolLogP', 'MolMR', 'fr_Al_COO', 'fr_Al_OH',
    'fr_Al_OH_noTert', 'fr_ArN', 'fr_Ar_COO', 'fr_Ar_N', 'fr_Ar_NH', 'fr_Ar_OH',
    'fr_COO', 'fr_COO2', 'fr_C_O', 'fr_C_O_noCOO', 'fr_C_S', 'fr_HOCCN',
    'fr_Imine', 'fr_NH0', 'fr_NH1', 'fr_NH2', 'fr_N_O', 'fr_Ndealkylation1',
    'fr_Ndealkylation2', 'fr_Nhpyrrole', 'fr_SH', 'fr_aldehyde', 'fr_alkyl_carbamate',
    'fr_alkyl_halide', 'fr_allylic_oxid', 'fr_amide', 'fr_amidine', 'fr_aniline',
    'fr_aryl_methyl', 'fr_azide', 'fr_azo', 'fr_barbitur', 'fr_benzene',
    'fr_benzodiazepine', 'fr_bicyclic', 'fr_diazo', 'fr_dihydropyridine',
    'fr_epoxide', 'fr_ester', 'fr_ether', 'fr_furan', 'fr_guanido', 'fr_halogen',
    'fr_hdrzine', 'fr_hdrzone', 'fr_imidazole', 'fr_imide', 'fr_isocyan',
    'fr_isothiocyan', 'fr_ketone', 'fr_ketone_Topliss', 'fr_lactam', 'fr_lactone',
    'fr_methoxy', 'fr_morpholine', 'fr_nitrile', 'fr_nitro', 'fr_nitro_arom',
    'fr_nitro_arom_nonortho', 'fr_nitroso', 'fr_oxazole', 'fr_oxime',
    'fr_para_hydroxylation', 'fr_phenol', 'fr_phenol_noOrthoHbond', 'fr_phos_acid',
    'fr_phos_ester', 'fr_piperdine', 'fr_piperzine', 'fr_priamide', 'fr_prisulfonamd',
    'fr_pyridine', 'fr_quatN', 'fr_sulfide', 'fr_sulfonamd', 'fr_sulfone',
    'fr_term_acetylene', 'fr_tetrazole', 'fr_thiazole', 'fr_thiocyan', 'fr_thiophene',
    'fr_unbrch_alkane', 'fr_urea'
]

# Number of features for model configuration
NUM_FEATURES = len(MOLECULE_FEATURES)


class RDKitDescriptorCalculator:
    """
    Robust RDKit descriptor calculator with comprehensive error handling.

    This class provides methods to calculate molecular descriptors from SMILES strings
    with fallback values for failed calculations to ensure training stability.
    """

    def __init__(self, use_cache: bool = True):
        """
        Initialize the descriptor calculator.

        Args:
            use_cache (bool): Whether to cache calculated descriptors (for future use)
        """
        self.use_cache = use_cache
        self.cache = {} if use_cache else None
        self.failed_smiles = set()  # Track failed SMILES for debugging

        # Pre-compute fallback values (median values from a representative dataset)
        self.fallback_values = self._get_fallback_values()

    def _get_fallback_values(self) -> np.ndarray:
        """
        Get fallback values for descriptors when calculation fails.

        These are reasonable default values based on typical molecular properties.
        In a production system, these could be computed from a representative dataset.

        Returns:
            np.ndarray: Array of fallback values for all descriptors
        """
        # Initialize with zeros and set reasonable defaults for key descriptors
        fallback = np.zeros(NUM_FEATURES)

        # Set some reasonable defaults for common descriptors
        feature_defaults = {
            'MolWt': 300.0,
            'HeavyAtomCount': 20.0,
            'RingCount': 2.0,
            'MolLogP': 2.0,
            'qed': 0.5,
            'TPSA': 60.0,
            'NumHAcceptors': 3.0,
            'NumHDonors': 2.0,
            'NumRotatableBonds': 5.0,
            'FractionCSP3': 0.3,
            'NumAromaticRings': 1.0,
        }

        for i, feature_name in enumerate(MOLECULE_FEATURES):
            if feature_name in feature_defaults:
                fallback[i] = feature_defaults[feature_name]

        return fallback

    def smiles_to_mol(self, smiles: str) -> Optional[Chem.Mol]:
        """
        Convert SMILES string to RDKit molecule object with error handling.

        Args:
            smiles (str): SMILES string

        Returns:
            Optional[Chem.Mol]: RDKit molecule object or None if conversion fails
        """
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return None

            # Try to sanitize the molecule
            Chem.SanitizeMol(mol)
            return mol
        except Exception:
            return None

    def calculate_single_descriptor(self, mol: Chem.Mol, descriptor_name: str) -> float:
        """
        Calculate a single descriptor with error handling.

        Args:
            mol (Chem.Mol): RDKit molecule object
            descriptor_name (str): Name of the descriptor to calculate

        Returns:
            float: Descriptor value or fallback value if calculation fails
        """
        try:
            if descriptor_name == 'qed':
                return QED.qed(mol)
            elif hasattr(Descriptors, descriptor_name):
                return getattr(Descriptors, descriptor_name)(mol)
            elif hasattr(rdMolDescriptors, descriptor_name):
                return getattr(rdMolDescriptors, descriptor_name)(mol)
            else:
                # Try to find the descriptor in other RDKit modules
                for module in [Descriptors, rdMolDescriptors]:
                    if hasattr(module, descriptor_name):
                        return getattr(module, descriptor_name)(mol)

                # If descriptor not found, return 0
                return 0.0

        except Exception:
            return 0.0

    def calculate_descriptors(self, smiles: str) -> np.ndarray:
        """
        Calculate all molecular descriptors for a given SMILES string.

        Args:
            smiles (str): SMILES string

        Returns:
            np.ndarray: Array of descriptor values with shape (NUM_FEATURES,)
        """
        # Check cache first
        if self.use_cache and smiles in self.cache:
            return self.cache[smiles]

        # Convert SMILES to molecule
        mol = self.smiles_to_mol(smiles)

        if mol is None:
            self.failed_smiles.add(smiles)
            result = self.fallback_values.copy()
        else:
            # Calculate all descriptors
            descriptors = []
            for descriptor_name in MOLECULE_FEATURES:
                value = self.calculate_single_descriptor(mol, descriptor_name)

                # Handle NaN, inf, or extremely large values
                if np.isnan(value) or np.isinf(value):
                    # Use fallback value for this descriptor
                    fallback_idx = MOLECULE_FEATURES.index(descriptor_name)
                    value = self.fallback_values[fallback_idx]
                elif abs(value) > 1000:
                    # Clip extremely large values to prevent training instability
                    value = np.sign(value) * min(abs(value), 1000.0)

                descriptors.append(value)

            result = np.array(descriptors, dtype=np.float32)

        # Cache the result
        if self.use_cache:
            self.cache[smiles] = result

        return result

    def get_feature_names(self) -> List[str]:
        """
        Get the list of feature names.

        Returns:
            List[str]: List of descriptor names
        """
        return MOLECULE_FEATURES.copy()

    def get_num_features(self) -> int:
        """
        Get the number of features.

        Returns:
            int: Number of molecular descriptors
        """
        return NUM_FEATURES

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the cache and failed calculations.

        Returns:
            Dict[str, Any]: Dictionary with cache statistics
        """
        return {
            'cache_size': len(self.cache) if self.cache else 0,
            'failed_smiles_count': len(self.failed_smiles),
            'cache_enabled': self.use_cache
        }


# Global descriptor calculator instance
_descriptor_calculator = None


def get_descriptor_calculator() -> RDKitDescriptorCalculator:
    """
    Get the global descriptor calculator instance (singleton pattern).

    Returns:
        RDKitDescriptorCalculator: Global descriptor calculator instance
    """
    global _descriptor_calculator
    if _descriptor_calculator is None:
        _descriptor_calculator = RDKitDescriptorCalculator(use_cache=True)
    return _descriptor_calculator


def calculate_molecular_descriptors(smiles: str) -> np.ndarray:
    """
    Convenience function to calculate molecular descriptors for a SMILES string.

    Args:
        smiles (str): SMILES string

    Returns:
        np.ndarray: Array of descriptor values
    """
    calculator = get_descriptor_calculator()
    return calculator.calculate_descriptors(smiles)


def batch_calculate_descriptors(smiles_list: List[str]) -> np.ndarray:
    """
    Calculate descriptors for a batch of SMILES strings.

    Args:
        smiles_list (List[str]): List of SMILES strings

    Returns:
        np.ndarray: Array of descriptor values with shape (len(smiles_list), NUM_FEATURES)
    """
    calculator = get_descriptor_calculator()
    results = []

    for smiles in smiles_list:
        descriptors = calculator.calculate_descriptors(smiles)
        results.append(descriptors)

    return np.array(results)
